SELECT


POSTREQUISITION.FAuditTime AS AUDITTIME,
POS<PERSON><PERSON><PERSON>UISITION.FNumber AS "NUMBER",
POSTREQUISITION.FBizDate AS BIZDATE,
POSTREQUISITION.FDescription AS DESCRIPTION,
STORAG<PERSON>ORGUNIT.FName_l2 AS "STORAGEORGUNIT.NAME",
CREATOR.FName_l2 AS "CREATOR.NAME",
AUDITOR.FName_l2 AS "AUDITOR.NAME",
ENTRIES.FDeliveryDate AS "ENTRIES.DELIVERYDATE",
ENTRIES.FDeliveryAddress AS "ENTRIES.DELIVERYADDRESS",
ENTRIES.FCustPurNumber AS "ENTRIES.CUSTPURNUMBER",
ENTRIES.FQty AS "ENTRIES.QTY",
ENTRIES.FAssistQty AS "ENTRIES.ASSISTQTY",

ENTRIES.FSaleOrderNumber AS "ENTRIES.SALEORDERNUMBER",
ADMINORGUNIT.FName_l2 AS "ADMINORGUNIT.NAME",

BASEUNIT.FName_l2 AS "BASEUNIT.NAME",


SALEGROUP.FName_l2 AS "SALEGROUP.NAME",

UNIT.FName_l2 AS "UNIT.NAME",

SALEPERSON.FName_l2 AS "SALEPERSON.NAME",
DELIVERYTYPE.FName_l2 AS "DELIVERYTYPE.NAME",

ORDERCUSTOMER.FName_l2 AS "ORDERCUSTOMER.NAME",


WAREHOUSE.FName_l2 AS "WAREHOUSE.NAME",

MATERIAL.FHelpCode AS "MATERIAL.HELPCODE",
MATERIAL.FSHORTNAME AS "MATERIAL.SHORTNAME",
MATERIAL.FGrossWeight AS "MATERIAL.GROSSWEIGHT",
MATERIAL.FNetWeight AS "MATERIAL.NETWEIGHT",

MATERIAL.FVolume AS "MATERIAL.VOLUME",

MATERIAL.FName_l2 AS "MATERIAL.NAME",

MATERIAL.FSimpleName AS "MATERIAL.SIMPLENAME",


MATERIALGROUP.FName_l2 AS "MATERIALGROUP.NAME",




SALEORDER.CFContractname AS "SALEORDER.CONTRACTNAME",
SALEORDER.CFContractphone AS "SALEORDER.CONTRACTPHONE",
SALEORDER.CFAdress AS "SALEORDER.ADRESS",
ENTRIES.CFZhongliang AS "ENTRIES.ZHONGLIANG",
ENTRIES.CFZs AS "ENTRIES.ZS",
ENTRIES.CFZhl AS "ENTRIES.ZHL",

CASE
    WHEN (MATERIALGROUP.FNumber LIKE '19010%') THEN '卫浴产品'
    WHEN (MATERIAL.FName_l2 LIKE '%公元HDPE超静音%') AND (MATERIAL.FName_l2 LIKE '%管件%') THEN '超静音管件'
    WHEN ((MATERIAL.FName_l2 LIKE '%PVC-U新风%') OR (MATERIAL.FName_l2 LIKE '%UPVC新风%') OR (MATERIAL.FName_l2 LIKE '%静音排水%') OR (MATERIAL.FName_l2 LIKE '%304不锈钢%') OR (MATERIAL.FName_l2 LIKE '%PVC排水%') OR (MATERIAL.FName_l2 LIKE '%PVC-U排水%') OR (MATERIAL.FName_l2 LIKE '%PVC排污%') OR (MATERIAL.FName_l2 LIKE '%PVC电工%') OR (MATERIAL.FName_l2 LIKE '%PVC普通排水%') OR (MATERIAL.FName_l2 LIKE '%HRS%') OR (MATERIAL.FName_l2 LIKE '%电工%') AND (MATERIAL.FName_l2 LIKE '%优家%') OR (MATERIAL.FName_l2 LIKE '%优家%') AND (MATERIAL.FName_l2 LIKE '%排水%') OR (MATERIAL.FName_l2 LIKE '%空调冷凝水%')) AND (MATERIAL.FName_l2 LIKE '%管材%') AND (MATERIAL.FName_l2 NOT LIKE '%线槽%') AND (MATERIAL.FName_l2 NOT LIKE '%保温%') AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PVC排水电工管材/优家电工管材'
    WHEN ((MATERIAL.FName_l2 LIKE '%PVC-U排水管件%') OR (MATERIAL.FName_l2 LIKE '%UPVC排水管件%') OR (MATERIAL.FName_l2 LIKE '%空调冷凝水管件%')) AND (MATERIAL.FName_l2 NOT LIKE '%检测%') AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PVC排水管件'
    WHEN (MATERIAL.FShortName LIKE '%50806%') THEN '50806'
    WHEN ((MATERIAL.FName_l2 LIKE '%公元PVC-U排水管件%') OR (MATERIAL.FName_l2 LIKE '%公元UPVC排水管件%') OR (MATERIAL.FName_l2 LIKE '%精杰PVC-U排水管件%') OR (MATERIAL.FName_l2 LIKE '%精杰UPVC排水管件%')) AND (MATERIAL.FName_l2 LIKE '%检测%') THEN '检测'
    WHEN ((MATERIAL.FName_l2 LIKE '%铁法兰盘%') OR (MATERIAL.FName_l2 LIKE '%铁法兰片%')) THEN '铁法兰'
    WHEN ((MATERIAL.FName_l2 LIKE '%PVC%') AND (MATERIAL.FName_l2 LIKE '%电工%') AND (MATERIAL.FName_l2 LIKE '%管件%') OR (MATERIAL.FName_l2 LIKE '%公元优家电工%')) AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PVC电工管件'
    WHEN ((MATERIAL.FName_l2 LIKE '%PP-R%') OR (MATERIAL.FName_l2 LIKE '%PPR%') OR (MATERIAL.FName_l2 LIKE '%PERT%') AND ((MATERIAL.FName_l2 LIKE '%管材%') OR (MATERIAL.FName_l2 LIKE '%管件%')) OR (MATERIAL.FName_l2 LIKE '%PE-RT%') AND ((MATERIAL.FName_l2 LIKE '%管材%') OR (MATERIAL.FName_l2 LIKE '%管件%')) OR (MATERIAL.FName_l2 LIKE '%燃气%') OR (MATERIAL.FName_l2 LIKE '%法兰盘%') AND (MATERIAL.FName_l2 NOT LIKE '%铁法兰盘%') OR (MATERIAL.FName_l2 LIKE '%PE%') AND (MATERIAL.FName_l2 LIKE '%管件%') OR (MATERIAL.FName_l2 LIKE '%金属%') AND (MATERIAL.FName_l2 LIKE '%管卡%') OR (MATERIAL.FName_l2 LIKE '%螺钉%') OR (MATERIAL.FName_l2 LIKE '%熔接套筒%') OR (MATERIAL.FName_l2 LIKE '%公元优家%') AND (MATERIAL.FName_l2 NOT LIKE '%公元优家吉谷%') AND (MATERIAL.FName_l2 NOT LIKE '%公元优家排水胶%') OR (MATERIAL.FName_l2 LIKE '%铜件%') OR (MATERIAL.FName_l2 LIKE '%PE-RT%') AND (MATERIAL.FName_l2 LIKE '%球阀%') OR (MATERIAL.FName_l2 LIKE '%PB%') OR (MATERIAL.FName_l2 LIKE '%体铜%') OR (MATERIAL.FName_l2 LIKE '%分集水器%') OR (MATERIAL.FName_l2 LIKE '%管塞%') OR (MATERIAL.FName_l2 LIKE '%过滤阀%')) AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PPR/PE管件/燃气管'
    WHEN ((MATERIAL.FName_l2 LIKE '%PE%') AND (MATERIAL.FName_l2 LIKE '%管材%') AND (MATERIAL.FName_l2 NOT LIKE '%PERT%') OR (MATERIAL.FName_l2 LIKE '%PE钢丝网%') OR (MATERIAL.FName_l2 LIKE '%PE电力电缆管%') OR (MATERIAL.FName_l2 LIKE '%PE建筑排水管材%')) AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PE管材管件'
    WHEN ((MATERIAL.FName_l2 LIKE '%PE%') OR (MATERIAL.FName_l2 LIKE '%PVC%')) AND (MATERIAL.FName_l2 LIKE '%波纹%') AND (MATERIAL.FName_l2 NOT LIKE '%样品%') THEN 'PE波纹管胶圈'
    WHEN (MATERIAL.FName_l2 LIKE '%样品%') THEN '样品'
    WHEN ((MATERIAL.FName_l2 LIKE '%HRS%') AND (MATERIAL.FName_l2 LIKE '%管件%') OR (MATERIAL.FName_l2 LIKE '%PVC给水%') AND (MATERIAL.FName_l2 LIKE '%管材%') OR (MATERIAL.FName_l2 LIKE '%空调保护管材%')) THEN 'PVC给水管材管件/配件'
    ELSE 'PVC给水管材管件/配件'
END AS "MATERIAL.NAME1",
ENTRIES.CFZongVolume AS "ENTRIES.ZONGVOLUME",
MATERIAL.FVolume AS "MATERIAL.VOLUME1",
CASE
    WHEN STORAGEORGUNIT.FNumber = 14 THEN ENTRIES.FAssociateQty
    ELSE ENTRIES.FQty
END AS "ENTRIES.ASSOCIATEQTY1",
(SELECT
    FBASECONVSRATE
FROM T_BD_MultiMeasureUnit
WHERE FMEASUREUNITID = 'sREAAADeFEFbglxX' AND FMATERIALID = ENTRIES.FMaterialID) AS BAOZHL,
'' AS QUERYFIELD,
CASE
    WHEN (MATERIAL.CFMaoWeight IS NULL) THEN (MATERIAL.FGrossWeight * ENTRIES.FQty)
    ELSE (MATERIAL.CFMaoWeight * ENTRIES.FQty)
END AS "MATERIAL.GROSSWEIGHT123",
DELIVERYTYPE.FName_l2 AS "DELIVERYTYPE.NAME1",
CASE
    WHEN ((MATERIALGROUP.FNumber LIKE '101%') OR (MATERIALGROUP.FNumber LIKE '102%') OR (MATERIALGROUP.FNumber LIKE '103%') OR (MATERIALGROUP.FNumber LIKE '125%') OR (MATERIALGROUP.FNumber LIKE '140%') OR (MATERIALGROUP.FNumber LIKE '13010%') OR (MATERIALGROUP.FNumber LIKE '13020%') OR (MATERIALGROUP.FNumber LIKE '13021%') OR (MATERIALGROUP.FNumber LIKE '13022%') OR (MATERIALGROUP.FNumber LIKE '2010201%')) THEN '管材'
    ELSE '管件/配件'
END AS FZZD1,
CASE
    WHEN MATERIAL.FNumber = '201010401001' THEN FLOOR((ENTRIES.FQty / 100))
    WHEN MATERIAL.FNumber = '201010401004' THEN FLOOR((ENTRIES.FQty / 30))
    WHEN MATERIAL.FNumber = '201010401008' THEN FLOOR((ENTRIES.FQty / 48))
    WHEN MATERIAL.FNumber = '201010401009' THEN FLOOR((ENTRIES.FQty / 20))
    WHEN MATERIAL.FNumber = '201010401012' THEN FLOOR((ENTRIES.FQty / 48))
    WHEN MATERIAL.FNumber = '201010402001' THEN FLOOR((ENTRIES.FQty / 48))
    WHEN MATERIAL.FNumber = '201010402004' THEN FLOOR((ENTRIES.FQty / 40))
    WHEN MATERIAL.FNumber = '201010402008' THEN FLOOR((ENTRIES.FQty / 15))
    WHEN MATERIAL.FNumber = '201020000140' THEN FLOOR((ENTRIES.FQty / 1000))
    WHEN MATERIAL.FNumber = '201020000141' THEN FLOOR((ENTRIES.FQty / 880))
    WHEN MATERIAL.FNumber = '201020000142' THEN FLOOR((ENTRIES.FQty / 550))
    WHEN MATERIAL.FNumber = '201020000143' THEN FLOOR((ENTRIES.FQty / 296))
    WHEN MATERIAL.FNumber = '201020000144' THEN FLOOR((ENTRIES.FQty / 200))
    WHEN MATERIAL.FNumber = '201020000415' THEN FLOOR((ENTRIES.FQty / 132))
    WHEN MATERIAL.FNumber = '201020000416' THEN FLOOR((ENTRIES.FQty / 336))
    WHEN MATERIAL.FNumber = '201020000417' THEN FLOOR((ENTRIES.FQty / 240))
    WHEN MATERIAL.FNumber = '201020000480' THEN FLOOR((ENTRIES.FQty / 44))
    ELSE ENTRIES.CFZs
END AS TSZS,
CASE
    WHEN MATERIAL.FNumber = '201010401001' THEN 100
    WHEN MATERIAL.FNumber = '201010401004' THEN 30
    WHEN MATERIAL.FNumber = '201010401008' THEN 48
    WHEN MATERIAL.FNumber = '201010401009' THEN 20
    WHEN MATERIAL.FNumber = '201010401012' THEN 48
    WHEN MATERIAL.FNumber = '201010402001' THEN 48
    WHEN MATERIAL.FNumber = '201010402004' THEN 40
    WHEN MATERIAL.FNumber = '201010402008' THEN 15
    WHEN MATERIAL.FNumber = '201020000140' THEN 1000
    WHEN MATERIAL.FNumber = '201020000141' THEN 880
    WHEN MATERIAL.FNumber = '201020000142' THEN 550
    WHEN MATERIAL.FNumber = '201020000143' THEN 296
    WHEN MATERIAL.FNumber = '201020000144' THEN 200
    WHEN MATERIAL.FNumber = '201020000415' THEN 132
    WHEN MATERIAL.FNumber = '201020000416' THEN 336
    WHEN MATERIAL.FNumber = '201020000417' THEN 240
    WHEN MATERIAL.FNumber = '201020000480' THEN 44
    ELSE ENTRIES.CFZhl
END AS TSZHL,
POSTREQUISITION.CFVehiclelength AS VEHICLELENGTH,
POSTREQUISITION.CFVehicleweight AS VEHICLEWEIGHT,
POSTREQUISITION.CFVehiclemanual AS VEHICLEMANUAL,
POSTREQUISITION.CFVehiclenumber AS VEHICLENUMBER,
POSTREQUISITION.CFSelfOwnBrand AS SELFOWNBRAND


FROM T_SD_PostRequisition POSTREQUISITION



LEFT OUTER JOIN T_ORG_Storage STORAGEORGUNIT
ON POSTREQUISITION.FStorageOrgUnitID = STORAGEORGUNIT.FID

LEFT OUTER JOIN T_PM_User CREATOR
ON POSTREQUISITION.FCreatorID = CREATOR.FID


LEFT OUTER JOIN T_PM_User AUDITOR
ON POSTREQUISITION.FAuditorID = AUDITOR.FID


LEFT OUTER JOIN T_SD_PostRequisitionEntry ENTRIES
ON POSTREQUISITION.FID = ENTRIES.FParentID

LEFT OUTER JOIN T_BD_AsstAttrValue ASSISTPROPERTY
ON ENTRIES.FAssistPropertyID = ASSISTPROPERTY.FID

LEFT OUTER JOIN T_ORG_Admin ADMINORGUNIT
ON ENTRIES.FAdminOrgUnitID = ADMINORGUNIT.FID

LEFT OUTER JOIN T_BD_MeasureUnit BASEUNIT
ON ENTRIES.FBaseUnitID = BASEUNIT.FID



LEFT OUTER JOIN T_BD_SaleGroup SALEGROUP
ON ENTRIES.FSaleGroupID = SALEGROUP.FID

LEFT OUTER JOIN T_BD_MeasureUnit UNIT
ON ENTRIES.FUnitID = UNIT.FID

LEFT OUTER JOIN T_BD_Person SALEPERSON
ON ENTRIES.FSalePersonID = SALEPERSON.FID

LEFT OUTER JOIN T_SCM_DeliveryType DELIVERYTYPE
ON ENTRIES.FDeliveryTypeID = DELIVERYTYPE.FID

LEFT OUTER JOIN T_BD_Customer ORDERCUSTOMER
ON ENTRIES.FOrderCustomerID = ORDERCUSTOMER.FID

LEFT OUTER JOIN T_DB_WAREHOUSE WAREHOUSE
ON ENTRIES.FWarehouseID = WAREHOUSE.FID

LEFT OUTER JOIN T_SCM_BillType E_SOURCEBILLTYPE
ON ENTRIES.FSourceBillTypeID = E_SOURCEBILLTYPE.FID

LEFT OUTER JOIN T_BD_Material MATERIAL
ON ENTRIES.FMaterialID = MATERIAL.FID

LEFT OUTER JOIN T_BD_MeasureUnit ASSISTUNIT
ON ENTRIES.FAssistUnitID = ASSISTUNIT.FID

LEFT OUTER JOIN T_DB_LOCATION LOCATION
ON ENTRIES.FLocationID = LOCATION.FID

LEFT OUTER JOIN T_BD_Customer DELIVERYCUSTOMER
ON ENTRIES.FDeliveryCustomerID = DELIVERYCUSTOMER.FID

LEFT OUTER JOIN T_BD_Customer RECEIVECUSTOMER
ON ENTRIES.FReceiveCustomerID = RECEIVECUSTOMER.FID

INNER JOIN T_BD_Customer PAYMENTCUSTOMER
ON ENTRIES.FPaymentCustomerID = PAYMENTCUSTOMER.FID

LEFT OUTER JOIN T_MM_Project PROJECT
ON ENTRIES.FprojectID = PROJECT.FID

LEFT OUTER JOIN T_MM_TrackNumber TRACKNUMBER
ON ENTRIES.FtrackNumberID = TRACKNUMBER.FID

LEFT OUTER JOIN T_SD_SaleOrder SALEORDER
ON ENTRIES.FSaleOrderID = SALEORDER.FID

LEFT OUTER JOIN T_BD_Country COUNTRY
ON ORDERCUSTOMER.FCountryID = COUNTRY.FID

LEFT OUTER JOIN T_ORG_Company INTERNALCOMPANY
ON ORDERCUSTOMER.FInternalCompanyID = INTERNALCOMPANY.FID

LEFT OUTER JOIN T_BD_City CITY
ON ORDERCUSTOMER.FCityID = CITY.FID

LEFT OUTER JOIN T_BD_Region REGION
ON ORDERCUSTOMER.FRegionID = REGION.FID

LEFT OUTER JOIN T_BD_Province PROVINCE
ON ORDERCUSTOMER.FProvince = PROVINCE.FID

LEFT OUTER JOIN T_BD_TaxData TAXDATA
ON ORDERCUSTOMER.FTaxDataID = TAXDATA.FID

LEFT OUTER JOIN T_BD_CustomerSaleInfo CUSTOMERSALEINFO
ON ENTRIES.FOrderCustomerID = CUSTOMERSALEINFO.FCustomerID

LEFT OUTER JOIN T_BD_MeasureUnit SEQUNIT
ON MATERIAL.FSeqUnitID = SEQUNIT.FID

LEFT OUTER JOIN T_BD_MeasureUnit WEIGHTUNIT
ON MATERIAL.FWeightUnit = WEIGHTUNIT.FID

LEFT OUTER JOIN T_BD_MaterialGroup MATERIALGROUP
ON MATERIAL.FMaterialGroupID = MATERIALGROUP.FID

LEFT OUTER JOIN T_BD_MeasureUnit LENGTHUNIT
ON MATERIAL.FLengthUnit = LENGTHUNIT.FID

LEFT OUTER JOIN T_BD_MeasureUnit VOLUMNUNIT
ON MATERIAL.FVolumnUnit = VOLUMNUNIT.FID

LEFT OUTER JOIN T_BD_MeasureUnit E_M_BASEUNIT
ON MATERIAL.FBaseUnit = E_M_BASEUNIT.FID

LEFT OUTER JOIN T_BD_MeasureUnit E_M_ASSISTUNIT
ON MATERIAL.FAssistUnit = E_M_ASSISTUNIT.FID

LEFT OUTER JOIN T_BD_CustomerLinkMan CUSTOMERLINKMAN
ON CUSTOMERSALEINFO.FID = CUSTOMERLINKMAN.FCustomerSaleID

WHERE (CASE
    WHEN STORAGEORGUNIT.FNumber = 14 THEN ENTRIES.FAssociateQty
    ELSE ENTRIES.FQty
END <> 0) and POSTREQUISITION.fnumber = 'SHPN0120250801597'

ORDER BY
"MATERIAL.NAME1" ASC,
"MATERIAL.SHORTNAME" ASC,
"NUMBER" ASC;