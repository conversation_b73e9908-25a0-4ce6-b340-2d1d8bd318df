SELECT 

"POSTREQUISITION".FCarrier AS "CA<PERSON><PERSON><PERSON>", 
"POSTR<PERSON>QUISITION".FAuditTime AS "AUDITTIME", 
"POSTREQUISITION".FBaseStatus AS "BASESTATUS", 
"POSTR<PERSON>QUISITION".FYear AS "YEAR", 
"POSTREQUISITION".FPeriod AS "PERIOD", 
"POSTREQUISITION".FModificationTime AS "MODIFICATIONTIME", 
"POSTREQUISITION".FNumber AS "NUMBER", 
"POSTREQUISITION".FBizDate AS "BIZDATE", 
"POSTREQUISITION".FDescription AS "DESCRIPTION", 
"<PERSON><PERSON><PERSON><PERSON><PERSON>UISITION".FHasEffected AS "HASEFFECTED", 
"POSTREQUISITION".FSourceBillID AS "SOURCEBILLID", 
"POSTREQUISITION".FSourceFunction AS "SOURCEFUNCTION", 
"<PERSON><PERSON><PERSON><PERSON>Q<PERSON>SITI<PERSON>".FCreateTime AS "CREATE<PERSON><PERSON>", 
"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>SI<PERSON><PERSON>".FLastUpdateTime AS "LASTUP<PERSON><PERSON>TI<PERSON>", 
"<PERSON><PERSON>TR<PERSON><PERSON><PERSON><PERSON>TION".FID AS "ID", 
"CURRENCY".FName_l2 AS "CURRENCY.NAME", 
"CURRENCY".FNumber AS "CURRENCY.NUMBER", 
"STORAGEORGUNIT".FName_l2 AS "STORAGEORGUNIT.NAME", 
"STORAGEORGUNIT".FNumber AS "STORAGEORGUNIT.NUMBER", 
"CREATOR".FNumber AS "CREATOR.NUMBER", 
"CREATOR".FName_l2 AS "CREATOR.NAME", 
"BILLTYPE".FName_l2 AS "BILLTYPE.NAME", 
"BILLTYPE".FNumber AS "BILLTYPE.NUMBER", 
"BIZTYPE".FName_l2 AS "BIZTYPE.NAME", 
"BIZTYPE".FNumber AS "BIZTYPE.NUMBER", 
"SOURCEBILLTYPE".FName_l2 AS "SOURCEBILLTYPE.NAME", 
"SOURCEBILLTYPE".FNumber AS "SOURCEBILLTYPE.NUMBER", 
"SALEORGUNIT".FName_l2 AS "SALEORGUNIT.NAME", 
"SALEORGUNIT".FNumber AS "SALEORGUNIT.NUMBER", 
"AUDITOR".FNumber AS "AUDITOR.NUMBER", 
"AUDITOR".FName_l2 AS "AUDITOR.NAME", 
"MODIFIER".FNumber AS "MODIFIER.NUMBER", 
"MODIFIER".FName_l2 AS "MODIFIER.NAME", 
"LASTUPDATEUSER".FNumber AS "LASTUPDATEUSER.NUMBER", 
"LASTUPDATEUSER".FName_l2 AS "LASTUPDATEUSER.NAME", 
"ENTRIES".FDeliveryDate AS "ENTRIES.DELIVERYDATE", 
"ENTRIES".FDeliveryAddress AS "ENTRIES.DELIVERYADDRESS", 
"ENTRIES".FTransLeadTime AS "ENTRIES.TRANSLEADTIME", 
"ENTRIES".FIsPresent AS "ENTRIES.ISPRESENT", 
"ENTRIES".FCustPurNumber AS "ENTRIES.CUSTPURNUMBER", 
"ENTRIES".FQty AS "ENTRIES.QTY", 
"ENTRIES".FAssistQty AS "ENTRIES.ASSISTQTY", 
"ENTRIES".FShippedQty AS "ENTRIES.SHIPPEDQTY", 
"ENTRIES".FUnShippedQty AS "ENTRIES.UNSHIPPEDQTY", 
"ENTRIES".FPrice AS "ENTRIES.PRICE", 
"ENTRIES".FAmount AS "ENTRIES.AMOUNT", 
"ENTRIES".FSendDate AS "ENTRIES.SENDDATE", 
"ENTRIES".FSaleOrderNumber AS "ENTRIES.SALEORDERNUMBER", 
"ENTRIES".FSaleOrderEntrySeq AS "ENTRIES.SALEORDERENTRYSEQ", 
"ENTRIES".FBaseQty AS "ENTRIES.BASEQTY", 
"ENTRIES".FShippedBaseQty AS "ENTRIES.SHIPPEDBASEQTY", 
"ENTRIES".FLocalAmount AS "ENTRIES.LOCALAMOUNT", 
"ENTRIES".FReason AS "ENTRIES.REASON", 
"ENTRIES".FSourceBillID AS "ENTRIES.SOURCEBILLID", 
"ENTRIES".FSourceBillNumber AS "ENTRIES.SOURCEBILLNUMBER", 
"ENTRIES".FSourceBillEntryID AS "ENTRIES.SOURCEBILLENTRYID", 
"ENTRIES".FSourceBillEntrySeq AS "ENTRIES.SOURCEBILLENTRYSEQ", 
"ENTRIES".FAssCoefficient AS "ENTRIES.ASSCOEFFICIENT", 
"ENTRIES".FBaseStatus AS "ENTRIES.BASESTATUS", 
"ENTRIES".FAssociateQty AS "ENTRIES.ASSOCIATEQTY", 
"ENTRIES".FRemark AS "ENTRIES.REMARK", 
"ENTRIES".FSeq AS "ENTRIES.SEQ", 
"ENTRIES".FID AS "ENTRIES.ID", 
"ASSISTPROPERTY".FNumber AS "ASSISTPROPERTY.NUMBER", 
"ASSISTPROPERTY".FName_l2 AS "ASSISTPROPERTY.NAME", 
"ADMINORGUNIT".FName_l2 AS "ADMINORGUNIT.NAME", 
"ADMINORGUNIT".FNumber AS "ADMINORGUNIT.NUMBER", 
"BASEUNIT".FName_l2 AS "BASEUNIT.NAME", 
"BASEUNIT".FNumber AS "BASEUNIT.NUMBER", 
"REASONCODE".FName_l2 AS "REASONCODE.NAME", 
"REASONCODE".FNumber AS "REASONCODE.NUMBER", 
"SALEGROUP".FName_l2 AS "SALEGROUP.NAME", 
"SALEGROUP".FNumber AS "SALEGROUP.NUMBER", 
"UNIT".FName_l2 AS "UNIT.NAME", 
"UNIT".FNumber AS "UNIT.NUMBER", 
"SALEPERSON".FName_l2 AS "SALEPERSON.NAME", 
"SALEPERSON".FNumber AS "SALEPERSON.NUMBER", 
"DELIVERYTYPE".FName_l2 AS "DELIVERYTYPE.NAME", 
"DELIVERYTYPE".FNumber AS "DELIVERYTYPE.NUMBER", 
"ORDERCUSTOMER".FUsedStatus AS "ORDERCUSTOMER.USEDSTATUS", 
"ORDERCUSTOMER".FArtificialPerson AS "ORDERCUSTOMER.ARTIFICIALPERS80", 
"ORDERCUSTOMER".FBizRegisterNo AS "ORDERCUSTOMER.BIZREGISTERNO", 
"ORDERCUSTOMER".FIsInternalCompany AS "ORDERCUSTOMER.ISINTERNALCOMP82", 
"ORDERCUSTOMER".FTxRegisterNo AS "ORDERCUSTOMER.TAXREGISTERNO", 
"ORDERCUSTOMER".FVersion AS "ORDERCUSTOMER.VERSION", 
"ORDERCUSTOMER".FEffectedStatus AS "ORDERCUSTOMER.EFFECTEDSTATUS", 
"ORDERCUSTOMER".FSuperiorUnit AS "ORDERCUSTOMER.SUPERIORUNIT", 
"ORDERCUSTOMER".FBarCode AS "ORDERCUSTOMER.BARCODE", 
"ORDERCUSTOMER".FMnemonicCode AS "ORDERCUSTOMER.MNEMONICCODE", 
"ORDERCUSTOMER".FBusiLicence AS "ORDERCUSTOMER.BUSILICENCE", 
"ORDERCUSTOMER".FBusiExequatur AS "ORDERCUSTOMER.BUSIEXEQUATUR", 
"ORDERCUSTOMER".FGSPAuthentication AS "ORDERCUSTOMER.GSPAUTHENTICAT91", 
"ORDERCUSTOMER".FCustomerKind AS "ORDERCUSTOMER.CUSTOMERKIND", 
"ORDERCUSTOMER".FForeignName AS "ORDERCUSTOMER.FOREIGNNAME", 
"ORDERCUSTOMER".FAddress AS "ORDERCUSTOMER.ADDRESS", 
"ORDERCUSTOMER".FInvoiceType AS "ORDERCUSTOMER.INVOICETYPE", 
"ORDERCUSTOMER".FIsCredited AS "ORDERCUSTOMER.ISCREDITED", 
"ORDERCUSTOMER".FTaxRate AS "ORDERCUSTOMER.TAXRATE", 
"ORDERCUSTOMER".FName_l2 AS "ORDERCUSTOMER.NAME", 
"ORDERCUSTOMER".FNumber AS "ORDERCUSTOMER.NUMBER", 
"ORDERCUSTOMER".FDescription_l2 AS "ORDERCUSTOMER.DESCRIPTION", 
"ORDERCUSTOMER".FSimpleName AS "ORDERCUSTOMER.SIMPLENAME", 
"ORDERCUSTOMER".FCreateTime AS "ORDERCUSTOMER.CREATETIME", 
"ORDERCUSTOMER".FLastUpdateTime AS "ORDERCUSTOMER.LASTUPDATETIME", 
"ENTRIES".FOrderCustomerID AS "ORDERCUSTOMER.ID", 
"COUNTRY".FName_l2 AS "COUNTRY.NAME", 
"COUNTRY".FNumber AS "COUNTRY.NUMBER", 
"INTERNALCOMPANY".FName_l2 AS "INTERNALCOMPANY.NAME", 
"INTERNALCOMPANY".FNumber AS "INTERNALCOMPANY.NUMBER", 
"CITY".FName_l2 AS "CITY.NAME", 
"CITY".FNumber AS "CITY.NUMBER", 
"REGION".FName_l2 AS "REGION.NAME", 
"REGION".FNumber AS "REGION.NUMBER", 
"PROVINCE".FName_l2 AS "PROVINCE.NAME", 
"PROVINCE".FNumber AS "PROVINCE.NUMBER", 
"TAXDATA".FType AS "TAXDATA.TYPE", 
"TAXDATA".FTaxRate AS "TAXDATA.TAXRATE", 
"TAXDATA".FName_l2 AS "TAXDATA.NAME", 
"TAXDATA".FNumber AS "TAXDATA.NUMBER", 
"WAREHOUSE".FName_l2 AS "WAREHOUSE.NAME", 
"WAREHOUSE".FNumber AS "WAREHOUSE.NUMBER", 
"E_SOURCEBILLTYPE".FName_l2 AS "E_SOURCEBILLTYPE.NAME", 
"E_SOURCEBILLTYPE".FNumber AS "E_SOURCEBILLTYPE.NUMBER", 
"MATERIAL".FVersion AS "MATERIAL.VERSION", 
"MATERIAL".FLongNumber AS "MATERIAL.LONGNUMBER", 
"MATERIAL".FShortName AS "MATERIAL.SHORTNAME", 
"MATERIAL".FModel AS "MATERIAL.MODEL", 
"MATERIAL".FPricePrecision AS "MATERIAL.PRICEPRECISION", 
"MATERIAL".FHelpCode AS "MATERIAL.HELPCODE", 
"MATERIAL".FBarCode AS "MATERIAL.BARCODE", 
"MATERIAL".FPictureNumber AS "MATERIAL.PICTURENUMBER", 
"MATERIAL".FGrossWeight AS "MATERIAL.GROSSWEIGHT", 
"MATERIAL".FNetWeight AS "MATERIAL.NETWEIGHT", 
"MATERIAL".FLength AS "MATERIAL.LENGTH", 
"MATERIAL".FWidth AS "MATERIAL.WIDTH", 
"MATERIAL".FHeight AS "MATERIAL.HEIGHT", 
"MATERIAL".FVolume AS "MATERIAL.VOLUME", 
"MATERIAL".FEffectedStatus AS "MATERIAL.EFFECTEDSTATUS", 
"MATERIAL".FAlias AS "MATERIAL.ALIAS", 
"MATERIAL".FForeignName AS "MATERIAL.FOREIGNNAME", 
"MATERIAL".FRegisteredMark AS "MATERIAL.REGISTEREDMARK", 
"MATERIAL".FWarrantNumber AS "MATERIAL.WARRANTNUMBER", 
"MATERIAL".FStatus AS "MATERIAL.STATUS", 
"MATERIAL".FName_l2 AS "MATERIAL.NAME", 
"MATERIAL".FNumber AS "MATERIAL.NUMBER", 
"MATERIAL".FDescription_l2 AS "MATERIAL.DESCRIPTION", 
"MATERIAL".FSimpleName AS "MATERIAL.SIMPLENAME", 
"MATERIAL".FCreateTime AS "MATERIAL.CREATETIME", 
"MATERIAL".FLastUpdateTime AS "MATERIAL.LASTUPDATETIME", 
"ENTRIES".FMaterialID AS "MATERIAL.ID", 
"SEQUNIT".FName_l2 AS "SEQUNIT.NAME", 
"SEQUNIT".FNumber AS "SEQUNIT.NUMBER", 
"WEIGHTUNIT".FName_l2 AS "WEIGHTUNIT.NAME", 
"WEIGHTUNIT".FNumber AS "WEIGHTUNIT.NUMBER", 
"MATERIALGROUP".FName_l2 AS "MATERIALGROUP.NAME", 
"MATERIALGROUP".FNumber AS "MATERIALGROUP.NUMBER", 
"LENGTHUNIT".FName_l2 AS "LENGTHUNIT.NAME", 
"LENGTHUNIT".FNumber AS "LENGTHUNIT.NUMBER", 
"VOLUMNUNIT".FName_l2 AS "VOLUMNUNIT.NAME", 
"VOLUMNUNIT".FNumber AS "VOLUMNUNIT.NUMBER", 
"E_M_BASEUNIT".FName_l2 AS "E_M_BASEUNIT.NAME", 
"E_M_BASEUNIT".FNumber AS "E_M_BASEUNIT.NUMBER", 
"E_M_ASSISTUNIT".FName_l2 AS "E_M_ASSISTUNIT.NAME", 
"E_M_ASSISTUNIT".FNumber AS "E_M_ASSISTUNIT.NUMBER", 
"ASSISTUNIT".FName_l2 AS "ASSISTUNIT.NAME", 
"ASSISTUNIT".FNumber AS "ASSISTUNIT.NUMBER", 
"ENTRIES".FLot AS "ENTRIES.LOT", 
"LOCATION".FName_l2 AS "LOCATION.NAME", 
"LOCATION".FNumber AS "LOCATION.NUMBER", 
"ENTRIES".FDeliveryCustomerID AS "DELIVERYCUSTOMER.ID", 
"DELIVERYCUSTOMER".FNumber AS "DELIVERYCUSTOMER.NUMBER", 
"DELIVERYCUSTOMER".FName_l2 AS "DELIVERYCUSTOMER.NAME", 
"RECEIVECUSTOMER".FName_l2 AS "RECEIVECUSTOMER.NAME", 
"RECEIVECUSTOMER".FNumber AS "RECEIVECUSTOMER.NUMBER", 
"ENTRIES".FReceiveCustomerID AS "RECEIVECUSTOMER.ID", 
"PAYMENTCUSTOMER".FName_l2 AS "PAYMENTCUSTOMER.NAME", 
"PAYMENTCUSTOMER".FNumber AS "PAYMENTCUSTOMER.NUMBER", 
"PAYMENTCUSTOMER".FID AS "PAYMENTCUSTOMER.ID", 
"PROJECT".FName_l2 AS "PROJECT.NAME", 
"PROJECT".FNumber AS "PROJECT.NUMBER", 
"TRACKNUMBER".FName_l2 AS "TRACKNUMBER.NAME", 
"TRACKNUMBER".FNumber AS "TRACKNUMBER.NUMBER", 
"ENTRIES".FprojectID AS "PROJECT.ID", 
"ENTRIES".FtrackNumberID AS "TRACKNUMBER.ID", 
"POSTREQUISITION".CFAddGoods AS "KJH", 
"POSTREQUISITION".CFReducibleGoods AS "BKJH", 
"DELIVERYTYPE".FDescription_l2 AS "DELIVERYTYPE.DESCRIPTION", 
"SALEORDER".FLinkMan AS "SALEORDER.LINKMAN", 
"SALEORDER".FCustomerPhone AS "SALEORDER.CUSTOMERPHONE", 
"SALEORDER".CFContractname AS "SALEORDER.CONTRACTNAME", 
"SALEORDER".CFContractphone AS "SALEORDER.CONTRACTPHONE", 
"SALEORDER".CFAdress AS "SALEORDER.ADRESS", 
"ENTRIES".CFZhongliang AS "ENTRIES.ZHONGLIANG", 
"ENTRIES".CFZs AS "ENTRIES.ZS", 
"ENTRIES".CFZhl AS "ENTRIES.ZHL", 
"POSTREQUISITION".CFKDTextField AS "KDTEXTFIELD", 
"POSTREQUISITION".CFKDTextArea AS "KDTEXTAREA", 
"WAREHOUSE".FName_l2 AS "WAREHOUSE.NAME1", 
CASE  WHEN ("MATERIALGROUP".FNumber LIKE '19010%') THEN '卫浴产品' WHEN ("MATERIAL".FName_l2 LIKE '%公元HDPE超静音%') AND ("MATERIAL".FName_l2 LIKE '%管件%') THEN '超静音管件' WHEN (("MATERIAL".FName_l2 LIKE '%PVC-U新风%') OR ("MATERIAL".FName_l2 LIKE '%UPVC新风%') OR ("MATERIAL".FName_l2 LIKE '%静音排水%') OR ("MATERIAL".FName_l2 LIKE '%304不锈钢%') OR ("MATERIAL".FName_l2 LIKE '%PVC排水%') OR ("MATERIAL".FName_l2 LIKE '%PVC-U排水%') OR ("MATERIAL".FName_l2 LIKE '%PVC排污%') OR ("MATERIAL".FName_l2 LIKE '%PVC电工%') OR ("MATERIAL".FName_l2 LIKE '%PVC普通排水%') OR ("MATERIAL".FName_l2 LIKE '%HRS%') OR ("MATERIAL".FName_l2 LIKE '%电工%') AND ("MATERIAL".FName_l2 LIKE '%优家%') OR ("MATERIAL".FName_l2 LIKE '%优家%') AND ("MATERIAL".FName_l2 LIKE '%排水%') OR ("MATERIAL".FName_l2 LIKE '%空调冷凝水%')) AND ("MATERIAL".FName_l2 LIKE '%管材%') AND ("MATERIAL".FName_l2 NOT LIKE '%线槽%') AND ("MATERIAL".FName_l2 NOT LIKE '%保温%') AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PVC排水电工管材/优家电工管材' WHEN (("MATERIAL".FName_l2 LIKE '%PVC-U排水管件%') OR ("MATERIAL".FName_l2 LIKE '%UPVC排水管件%') OR ("MATERIAL".FName_l2 LIKE '%空调冷凝水管件%')) AND ("MATERIAL".FName_l2 NOT LIKE '%检测%') AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PVC排水管件' WHEN ("MATERIAL".FShortName LIKE '%50806%') THEN '50806' WHEN (("MATERIAL".FName_l2 LIKE '%公元PVC-U排水管件%') OR ("MATERIAL".FName_l2 LIKE '%公元UPVC排水管件%') OR ("MATERIAL".FName_l2 LIKE '%精杰PVC-U排水管件%') OR ("MATERIAL".FName_l2 LIKE '%精杰UPVC排水管件%')) AND ("MATERIAL".FName_l2 LIKE '%检测%') THEN '检测' WHEN (("MATERIAL".FName_l2 LIKE '%铁法兰盘%') OR ("MATERIAL".FName_l2 LIKE '%铁法兰片%')) THEN '铁法兰' WHEN (("MATERIAL".FName_l2 LIKE '%PVC%') AND ("MATERIAL".FName_l2 LIKE '%电工%') AND ("MATERIAL".FName_l2 LIKE '%管件%') OR ("MATERIAL".FName_l2 LIKE '%公元优家电工%')) AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PVC电工管件' WHEN (("MATERIAL".FName_l2 LIKE '%PP-R%') OR ("MATERIAL".FName_l2 LIKE '%PPR%') OR ("MATERIAL".FName_l2 LIKE '%PERT%') AND (("MATERIAL".FName_l2 LIKE '%管材%') OR ("MATERIAL".FName_l2 LIKE '%管件%')) OR ("MATERIAL".FName_l2 LIKE '%PE-RT%') AND (("MATERIAL".FName_l2 LIKE '%管材%') OR ("MATERIAL".FName_l2 LIKE '%管件%')) OR ("MATERIAL".FName_l2 LIKE '%燃气%') OR ("MATERIAL".FName_l2 LIKE '%法兰盘%') AND ("MATERIAL".FName_l2 NOT LIKE '%铁法兰盘%') OR ("MATERIAL".FName_l2 LIKE '%PE%') AND ("MATERIAL".FName_l2 LIKE '%管件%') OR ("MATERIAL".FName_l2 LIKE '%金属%') AND ("MATERIAL".FName_l2 LIKE '%管卡%') OR ("MATERIAL".FName_l2 LIKE '%螺钉%') OR ("MATERIAL".FName_l2 LIKE '%熔接套筒%') OR ("MATERIAL".FName_l2 LIKE '%公元优家%') AND ("MATERIAL".FName_l2 NOT LIKE '%公元优家吉谷%') AND ("MATERIAL".FName_l2 NOT LIKE '%公元优家排水胶%') OR ("MATERIAL".FName_l2 LIKE '%铜件%') OR ("MATERIAL".FName_l2 LIKE '%PE-RT%') AND ("MATERIAL".FName_l2 LIKE '%球阀%') OR ("MATERIAL".FName_l2 LIKE '%PB%') OR ("MATERIAL".FName_l2 LIKE '%体铜%') OR ("MATERIAL".FName_l2 LIKE '%分集水器%') OR ("MATERIAL".FName_l2 LIKE '%管塞%') OR ("MATERIAL".FName_l2 LIKE '%过滤阀%')) AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PPR/PE管件/燃气管' WHEN (("MATERIAL".FName_l2 LIKE '%PE%') AND ("MATERIAL".FName_l2 LIKE '%管材%') AND ("MATERIAL".FName_l2 NOT LIKE '%PERT%') OR ("MATERIAL".FName_l2 LIKE '%PE钢丝网%') OR ("MATERIAL".FName_l2 LIKE '%PE电力电缆管%') OR ("MATERIAL".FName_l2 LIKE '%PE建筑排水管材%')) AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PE管材管件' WHEN (("MATERIAL".FName_l2 LIKE '%PE%') OR ("MATERIAL".FName_l2 LIKE '%PVC%')) AND ("MATERIAL".FName_l2 LIKE '%波纹%') AND ("MATERIAL".FName_l2 NOT LIKE '%样品%') THEN 'PE波纹管胶圈' WHEN ("MATERIAL".FName_l2 LIKE '%样品%') THEN '样品' WHEN (("MATERIAL".FName_l2 LIKE '%HRS%') AND ("MATERIAL".FName_l2 LIKE '%管件%') OR ("MATERIAL".FName_l2 LIKE '%PVC给水%') AND ("MATERIAL".FName_l2 LIKE '%管材%') OR ("MATERIAL".FName_l2 LIKE '%空调保护管材%')) THEN 'PVC给水管材管件/配件' ELSE 'PVC给水管材管件/配件' END AS "MATERIAL.NAME1", 
"ENTRIES".CFZongVolume AS "ENTRIES.ZONGVOLUME", 
"MATERIAL".FVolume AS "MATERIAL.VOLUME1", 
CASE  WHEN "STORAGEORGUNIT".FNumber = 14 THEN "ENTRIES".FAssociateQty ELSE "ENTRIES".FQty END AS "ENTRIES.ASSOCIATEQTY1", 
(SELECT 

FBASECONVSRATE

FROM T_BD_MultiMeasureUnit

WHERE FMEASUREUNITID = 'sREAAADeFEFbglxX' AND FMATERIALID = "ENTRIES".FMaterialID) AS "BAOZHL", 
'' AS "QUERYFIELD", 
CASE  WHEN ("MATERIAL".CFMaoWeight IS NULL) THEN ("MATERIAL".FGrossWeight * "ENTRIES".FQty) ELSE ("MATERIAL".CFMaoWeight * "ENTRIES".FQty) END AS "MATERIAL.GROSSWEIGHT123", 
"DELIVERYTYPE".FName_l2 AS "DELIVERYTYPE.NAME1", 
CASE  WHEN (("MATERIALGROUP".FNumber LIKE '101%') OR ("MATERIALGROUP".FNumber LIKE '102%') OR ("MATERIALGROUP".FNumber LIKE '103%') OR ("MATERIALGROUP".FNumber LIKE '125%') OR ("MATERIALGROUP".FNumber LIKE '140%') OR ("MATERIALGROUP".FNumber LIKE '13010%') OR ("MATERIALGROUP".FNumber LIKE '13020%') OR ("MATERIALGROUP".FNumber LIKE '13021%') OR ("MATERIALGROUP".FNumber LIKE '13022%') OR ("MATERIALGROUP".FNumber LIKE '2010201%')) THEN '管材' ELSE '管件/配件' END AS "FZZD1", 
CASE  WHEN "MATERIAL".FNumber = '201010401001' THEN FLOOR(("ENTRIES".FQty / 100)) WHEN "MATERIAL".FNumber = '201010401004' THEN FLOOR(("ENTRIES".FQty / 30)) WHEN "MATERIAL".FNumber = '201010401008' THEN FLOOR(("ENTRIES".FQty / 48)) WHEN "MATERIAL".FNumber = '201010401009' THEN FLOOR(("ENTRIES".FQty / 20)) WHEN "MATERIAL".FNumber = '201010401012' THEN FLOOR(("ENTRIES".FQty / 48)) WHEN "MATERIAL".FNumber = '201010402001' THEN FLOOR(("ENTRIES".FQty / 48)) WHEN "MATERIAL".FNumber = '201010402004' THEN FLOOR(("ENTRIES".FQty / 40)) WHEN "MATERIAL".FNumber = '201010402008' THEN FLOOR(("ENTRIES".FQty / 15)) WHEN "MATERIAL".FNumber = '201020000140' THEN FLOOR(("ENTRIES".FQty / 1000)) WHEN "MATERIAL".FNumber = '201020000141' THEN FLOOR(("ENTRIES".FQty / 880)) WHEN "MATERIAL".FNumber = '201020000142' THEN FLOOR(("ENTRIES".FQty / 550)) WHEN "MATERIAL".FNumber = '201020000143' THEN FLOOR(("ENTRIES".FQty / 296)) WHEN "MATERIAL".FNumber = '201020000144' THEN FLOOR(("ENTRIES".FQty / 200)) WHEN "MATERIAL".FNumber = '201020000415' THEN FLOOR(("ENTRIES".FQty / 132)) WHEN "MATERIAL".FNumber = '201020000416' THEN FLOOR(("ENTRIES".FQty / 336)) WHEN "MATERIAL".FNumber = '201020000417' THEN FLOOR(("ENTRIES".FQty / 240)) WHEN "MATERIAL".FNumber = '201020000480' THEN FLOOR(("ENTRIES".FQty / 44)) ELSE "ENTRIES".CFZs END AS "TSZS", 
CASE  WHEN "MATERIAL".FNumber = '201010401001' THEN 100 WHEN "MATERIAL".FNumber = '201010401004' THEN 30 WHEN "MATERIAL".FNumber = '201010401008' THEN 48 WHEN "MATERIAL".FNumber = '201010401009' THEN 20 WHEN "MATERIAL".FNumber = '201010401012' THEN 48 WHEN "MATERIAL".FNumber = '201010402001' THEN 48 WHEN "MATERIAL".FNumber = '201010402004' THEN 40 WHEN "MATERIAL".FNumber = '201010402008' THEN 15 WHEN "MATERIAL".FNumber = '201020000140' THEN 1000 WHEN "MATERIAL".FNumber = '201020000141' THEN 880 WHEN "MATERIAL".FNumber = '201020000142' THEN 550 WHEN "MATERIAL".FNumber = '201020000143' THEN 296 WHEN "MATERIAL".FNumber = '201020000144' THEN 200 WHEN "MATERIAL".FNumber = '201020000415' THEN 132 WHEN "MATERIAL".FNumber = '201020000416' THEN 336 WHEN "MATERIAL".FNumber = '201020000417' THEN 240 WHEN "MATERIAL".FNumber = '201020000480' THEN 44 ELSE "ENTRIES".CFZhl END AS "TSZHL", 
"POSTREQUISITION".CFVehiclelength AS "VEHICLELENGTH", 
"POSTREQUISITION".CFVehicleweight AS "VEHICLEWEIGHT", 
"POSTREQUISITION".CFVehiclemanual AS "VEHICLEMANUAL", 
"POSTREQUISITION".CFVehiclenumber AS "VEHICLENUMBER", 
"POSTREQUISITION".CFSelfOwnBrand AS "SELFOWNBRAND", 
"MATERIALGROUP".FName_l2 AS "MATERIALGROUP.NAME1", 
"ORDERCUSTOMER".CFMarks AS "ORDERCUSTOMER.MARKS", 
"ORDERCUSTOMER".FBusiExequatur AS "ORDERCUSTOMER.BUSIEXEQUATUR1"

FROM T_SD_PostRequisition AS "POSTREQUISITION"

LEFT OUTER JOIN T_BD_Currency AS "CURRENCY"
ON "POSTREQUISITION".FCurrencyID = "CURRENCY".FID

LEFT OUTER JOIN T_ORG_Storage AS "STORAGEORGUNIT"
ON "POSTREQUISITION".FStorageOrgUnitID = "STORAGEORGUNIT".FID

LEFT OUTER JOIN T_PM_User AS "CREATOR"
ON "POSTREQUISITION".FCreatorID = "CREATOR".FID

LEFT OUTER JOIN T_SCM_BillType AS "BILLTYPE"
ON "POSTREQUISITION".FBillTypeID = "BILLTYPE".FID

LEFT OUTER JOIN T_SCM_BizType AS "BIZTYPE"
ON "POSTREQUISITION".FBizTypeID = "BIZTYPE".FID

LEFT OUTER JOIN T_SCM_BillType AS "SOURCEBILLTYPE"
ON "POSTREQUISITION".FSourceBillTypeID = "SOURCEBILLTYPE".FID

LEFT OUTER JOIN T_ORG_Sale AS "SALEORGUNIT"
ON "POSTREQUISITION".FSaleOrgUnitID = "SALEORGUNIT".FID

LEFT OUTER JOIN T_PM_User AS "AUDITOR"
ON "POSTREQUISITION".FAuditorID = "AUDITOR".FID

LEFT OUTER JOIN T_PM_User AS "MODIFIER"
ON "POSTREQUISITION".FModifierID = "MODIFIER".FID

LEFT OUTER JOIN T_PM_User AS "LASTUPDATEUSER"
ON "POSTREQUISITION".FLastUpdateUserID = "LASTUPDATEUSER".FID

LEFT OUTER JOIN T_SD_PostRequisitionEntry AS "ENTRIES"
ON "POSTREQUISITION".FID = "ENTRIES".FParentID

LEFT OUTER JOIN T_BD_AsstAttrValue AS "ASSISTPROPERTY"
ON "ENTRIES".FAssistPropertyID = "ASSISTPROPERTY".FID

LEFT OUTER JOIN T_ORG_Admin AS "ADMINORGUNIT"
ON "ENTRIES".FAdminOrgUnitID = "ADMINORGUNIT".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "BASEUNIT"
ON "ENTRIES".FBaseUnitID = "BASEUNIT".FID

LEFT OUTER JOIN T_SCM_ReasonCode AS "REASONCODE"
ON "ENTRIES".FReasonCodeID = "REASONCODE".FID

LEFT OUTER JOIN T_BD_SaleGroup AS "SALEGROUP"
ON "ENTRIES".FSaleGroupID = "SALEGROUP".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "UNIT"
ON "ENTRIES".FUnitID = "UNIT".FID

LEFT OUTER JOIN T_BD_Person AS "SALEPERSON"
ON "ENTRIES".FSalePersonID = "SALEPERSON".FID

LEFT OUTER JOIN T_SCM_DeliveryType AS "DELIVERYTYPE"
ON "ENTRIES".FDeliveryTypeID = "DELIVERYTYPE".FID

LEFT OUTER JOIN T_BD_Customer AS "ORDERCUSTOMER"
ON "ENTRIES".FOrderCustomerID = "ORDERCUSTOMER".FID

LEFT OUTER JOIN T_DB_WAREHOUSE AS "WAREHOUSE"
ON "ENTRIES".FWarehouseID = "WAREHOUSE".FID

LEFT OUTER JOIN T_SCM_BillType AS "E_SOURCEBILLTYPE"
ON "ENTRIES".FSourceBillTypeID = "E_SOURCEBILLTYPE".FID

LEFT OUTER JOIN T_BD_Material AS "MATERIAL"
ON "ENTRIES".FMaterialID = "MATERIAL".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "ASSISTUNIT"
ON "ENTRIES".FAssistUnitID = "ASSISTUNIT".FID

LEFT OUTER JOIN T_DB_LOCATION AS "LOCATION"
ON "ENTRIES".FLocationID = "LOCATION".FID

LEFT OUTER JOIN T_BD_Customer AS "DELIVERYCUSTOMER"
ON "ENTRIES".FDeliveryCustomerID = "DELIVERYCUSTOMER".FID

LEFT OUTER JOIN T_BD_Customer AS "RECEIVECUSTOMER"
ON "ENTRIES".FReceiveCustomerID = "RECEIVECUSTOMER".FID

INNER JOIN T_BD_Customer AS "PAYMENTCUSTOMER"
ON "ENTRIES".FPaymentCustomerID = "PAYMENTCUSTOMER".FID

LEFT OUTER JOIN T_MM_Project AS "PROJECT"
ON "ENTRIES".FprojectID = "PROJECT".FID

LEFT OUTER JOIN T_MM_TrackNumber AS "TRACKNUMBER"
ON "ENTRIES".FtrackNumberID = "TRACKNUMBER".FID

LEFT OUTER JOIN T_SD_SaleOrder AS "SALEORDER"
ON "ENTRIES".FSaleOrderID = "SALEORDER".FID

LEFT OUTER JOIN T_BD_Country AS "COUNTRY"
ON "ORDERCUSTOMER".FCountryID = "COUNTRY".FID

LEFT OUTER JOIN T_ORG_Company AS "INTERNALCOMPANY"
ON "ORDERCUSTOMER".FInternalCompanyID = "INTERNALCOMPANY".FID

LEFT OUTER JOIN T_BD_City AS "CITY"
ON "ORDERCUSTOMER".FCityID = "CITY".FID

LEFT OUTER JOIN T_BD_Region AS "REGION"
ON "ORDERCUSTOMER".FRegionID = "REGION".FID

LEFT OUTER JOIN T_BD_Province AS "PROVINCE"
ON "ORDERCUSTOMER".FProvince = "PROVINCE".FID

LEFT OUTER JOIN T_BD_TaxData AS "TAXDATA"
ON "ORDERCUSTOMER".FTaxDataID = "TAXDATA".FID

LEFT OUTER JOIN T_BD_CustomerSaleInfo AS "CUSTOMERSALEINFO"
ON "ENTRIES".FOrderCustomerID = "CUSTOMERSALEINFO".FCustomerID

LEFT OUTER JOIN T_BD_MeasureUnit AS "SEQUNIT"
ON "MATERIAL".FSeqUnitID = "SEQUNIT".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "WEIGHTUNIT"
ON "MATERIAL".FWeightUnit = "WEIGHTUNIT".FID

LEFT OUTER JOIN T_BD_MaterialGroup AS "MATERIALGROUP"
ON "MATERIAL".FMaterialGroupID = "MATERIALGROUP".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "LENGTHUNIT"
ON "MATERIAL".FLengthUnit = "LENGTHUNIT".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "VOLUMNUNIT"
ON "MATERIAL".FVolumnUnit = "VOLUMNUNIT".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "E_M_BASEUNIT"
ON "MATERIAL".FBaseUnit = "E_M_BASEUNIT".FID

LEFT OUTER JOIN T_BD_MeasureUnit AS "E_M_ASSISTUNIT"
ON "MATERIAL".FAssistUnit = "E_M_ASSISTUNIT".FID

LEFT OUTER JOIN T_BD_CustomerLinkMan AS "CUSTOMERLINKMAN"
ON "CUSTOMERSALEINFO".FID = "CUSTOMERLINKMAN".FCustomerSaleID

WHERE (CASE  WHEN "STORAGEORGUNIT".FNumber = 14 THEN "ENTRIES".FAssociateQty ELSE "ENTRIES".FQty END <> 0)

ORDER BY 
"MATERIAL.NAME1" ASC,
"MATERIAL.SHORTNAME" ASC,
"NUMBER" ASC